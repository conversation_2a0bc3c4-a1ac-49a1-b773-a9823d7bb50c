<template>
  <a-drawer v-model:open="visible" :width="1200" title="查看商品" @close="handleClose" :maskClosable="false" :keyboard="false" :destroyOnClose="true">
    <a-spin :spinning="loading">
      <BaseForm />
      <div class="drawer-title">证书信息</div>
      <div class="px-20 mb-24">
        <a-space class="mb-16">
          <div class="text-16px font-bold c-#000">商品所需许可证</div>
        </a-space>
        <div class="c-#999 mb-24">
          <div>温馨提示：</div>
          <div>1:涉及护肤品商品生产，如：洗护用品，膏霜乳液制品，唇膏、唇彩、眉笔、唇线笔、发蜡，牙膏类商品，清洁类商品等，需上传《化妆品生产许可证》；</div>
          <div>2. 涉及食品生产，如：粮食加工品、油脂及其制品，调味品，肉制品，乳制品，饮料方便食品，冷冻饮品，速冻食品等，需上传《食品生产许可证》；</div>
          <div>3. 涉及药品生产，如：化学药剂，中药制剂，生物制品，原材料药及药用辅料，特殊管理药品等，需上传《药品生产许可证》；</div>
          <div>4. 涉及工业品生产，如：建筑用钢筋，水泥，广播电视传输设备，电线电缆，危险化学品，化肥等，需上传《工业产品生产许可证》；</div>
          <div>5. 涉及医疗器械相关生产，如：基础耗材，护理用品，诊断监护类，治疗康复类，手术耗材类，医用卫生材料等，需上传《医疗器械生产许可证》；</div>
        </div>
        <vxe-table :data="[{}]" size="small" border class="mb-24 vtable">
          <vxe-column v-for="item in permitCloumns" :key="item.field" :field="item.title" :title="item.title">
            <template #default>
              <a-flex>
                <a-button type="link" class="!px-8" @click="handleShowPermitModal(item.field)">查看文件</a-button>
                <div>
                  <a-badge :count="getCount(item.field)" />
                </div>
              </a-flex>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div>
        <div class="drawer-title">商品图片/视频</div>
        <a-form layout="vertical">
          <div class="px-20">
            <a-form-item :rules="{ required: true }">
              <template #label>
                <a-space>
                  <span>商品主图</span>
                  <a-button v-show="fileObj.shopFileList?.length" type="link" @click="handleDownload('shopFileList')">全部下载</a-button>
                </a-space>
              </template>
              <div>
                <BaseFileUpload
                  v-if="fileObj.shopFileList?.length"
                  class="uploader main-image"
                  :disabled="true"
                  multiple
                  v-model:file-list="fileObj.shopFileList"
                  accept="image/png, image/jpeg, image/gif"
                ></BaseFileUpload>
                <span v-else>--</span>
              </div>
            </a-form-item>
            <a-form-item :rules="{ required: true }">
              <template #label>
                <a-space>
                  <span>商品SKU图</span>
                  <a-button v-show="fileObj.skuFileList?.length" type="link" @click="handleDownload('skuFileList')">全部下载</a-button>
                </a-space>
              </template>
              <div>
                <BaseFileUpload
                  v-if="fileObj.skuFileList?.length"
                  class="uploader"
                  :disabled="true"
                  multiple
                  v-model:file-list="fileObj.skuFileList"
                  accept="image/png, image/jpeg, image/gif"
                ></BaseFileUpload>
                <span v-else>--</span>
              </div>
            </a-form-item>
            <a-form-item label="商品视频">
              <div>
                <BaseFileUpload
                  v-if="fileObj.videoFileList?.length"
                  class="uploader"
                  :disabled="true"
                  multiple
                  v-model:file-list="fileObj.videoFileList"
                  accept="video/mp4"
                  list-type="picture-card"
                ></BaseFileUpload>
                <span v-else>--</span>
              </div>
            </a-form-item>
          </div>
          <div class="drawer-title">商品详情</div>
          <div class="px-20">
            <div class="mb-8">
              <a-radio-group v-model:value="language" button-style="solid">
                <a-radio-button value="cn">中文</a-radio-button>
                <a-radio-button value="en">英文</a-radio-button>
              </a-radio-group>
              <a-button v-show="fileObj.cnFileList?.length || fileObj.enFileList?.length" type="link" @click="handleDownload('detailFileList')">全部下载</a-button>
            </div>
            <a-form-item>
              <template #label>
                <a-space :size="16">
                  <span>商品详情页</span>
                  <div class="flex items-center c-#999">
                    <InfoCircleOutlined />
                    支持 JPEG/JPG/PNG/GIF 格式；单张≤5MB；宽度>=750px，建议 750px~1600px（高度自适应）；最多不超过15张。
                  </div>
                </a-space>
              </template>
              <div class="c-#d33333">我们将按照提交时的图片顺序展示详情页图片，请严格按照详情页顺序上传或调整，以确保详情页正确展示。</div>
              <BaseFileUploadMode class="mt-16" v-model:file-list="fileObj.cnFileList" v-show="language === 'cn'" :disabled="true" />
              <BaseFileUploadMode class="mt-16" v-model:file-list="fileObj.enFileList" v-show="language === 'en'" :disabled="true" />
            </a-form-item>
          </div>
        </a-form>
      </div>
      <UserForm />
    </a-spin>
    <ProductFileSelectModal v-if="fileSelectModalVisible" v-model:visible="fileSelectModalVisible" :select-file-list="showFileList" :disabled="true" />
  </a-drawer>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { GetProduct, GetCategoryOption, DownloadImagesZip } from '@/servers/SupplierProduceStock'
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
import { useTemplate } from '@/hook/useTemplate'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import BaseFileUpload from '@/components/BaseFileUpload/index.vue'
import BaseFileUploadMode from '@/components/BaseFileUploadMode/index.vue'
import { ViewByFileIdCommon } from '@/servers/Common'
import { GetFileList } from '@/servers/companyInfo'
import Decimal from 'decimal.js'
import { getCommonOption } from '@/utils'
import ProductAddStepSecondTable from './ProductAddStepSecondTable.vue'
import ProductFileSelectModal from './ProductFileSelectModal.vue'

const props = defineProps<{
  id: number
  isEdit: boolean
}>()

// 抽屉是否显示
const visible = defineModel<boolean>('visible', { required: true })
// 加载状态
const loading = ref(false)
// 商品信息
const productInfo = ref<any>({})

const productTypeOptions = [
  { label: '新品', value: 1 },
  { label: '定制品', value: 2 },
  { label: '物料', value: 3 },
]

/**
 * 根据选品状态控制字段显示的计算属性
 * 选品状态枚举值：
 * - 0: 待选品
 * - 50: 已驳回
 * - 60: 已拒绝
 * - 40: 完成选品
 */

// 计算属性：判断是否显示协商后定价
const shouldShowAgreedPrice = computed(() => {
  const selectionStatus = productInfo.value.selection_status
  // 完成选品状态(40)时显示协商后定价
  return selectionStatus === 40
})

// 计算属性：判断是否显示供货信息
const shouldShowSupplyInfo = computed(() => {
  const selectionStatus = productInfo.value.selection_status
  // 完成选品状态(40)时显示供货信息
  return selectionStatus === 40
})

// 计算属性：判断是否显示店铺供货价
const shouldShowStoreSupplyPrice = computed(() => {
  const selectionStatus = productInfo.value.selection_status
  // 完成选品状态(40)时显示店铺供货价
  return selectionStatus === 40
})

// 存储模板配置的响应式数据
const templateFormConfig = ref<BaseFormItem[]>([])
// 基础信息表单配置 - 改为计算属性以支持动态显示
const basicDrawerForm = computed<BaseFormItem[]>(() => {
  const baseFields: BaseFormItem[] = [
    {
      type: 'text',
      label: '商品名称(中)',
      key: 'product_name',
      span: 6,
    },
    {
      type: 'text',
      label: '商品名称(英)',
      key: 'product_name_en',
      span: 6,
    },
    {
      type: 'text',
      label: '供应商商品编码',
      key: 'supplier_product_number',
      span: 6,
    },
    {
      type: 'text',
      label: '商品类型',
      span: 6,
      key: 'product_type',
    },
    {
      type: 'text',
      label: '是否白牌',
      span: 6,
      key: 'is_white_brand_string',
    },
    {
      type: 'text',
      label: '商品品牌',
      span: 6,
      key: 'brand_name',
    },
    {
      type: 'text',
      label: '商品类目',
      span: 6,
      key: 'category_id',
    },
    {
      type: 'text',
      label: '发货时效',
      span: 6,
      key: 'delivery_time',
      afterSlot: () => h('span', null, 'h(小时)'),
    },
    {
      type: 'text',
      label: '发货地',
      span: 6,
      key: 'delivery_location',
    },
    {
      type: 'text',
      label: '商品卖点',
      span: 6,
      key: 'selling_points',
    },
    {
      type: 'text',
      label: '供应商名称',
      key: 'supplier_name',
      span: 6,
    },
    {
      type: 'text',
      label: '供应商编码',
      key: 'supplier_number',
      span: 6,
    },
    {
      type: 'text',
      label: '选品状态',
      key: 'selection_status_string',
      span: 6,
    },
    // 新增
    {
      type: 'text',
      label: '平台商品编码',
      key: 'product_number',
      span: 6,
    },
    {
      type: 'text',
      label: 'PLM商品编码',
      key: 'plm_product_code',
      span: 6,
    },
    {
      type: 'text',
      label: '聚水潭商品编码',
      key: 'jst_sku_id',
      span: 6,
    },
    {
      type: 'text',
      label: '选品意见',
      key: 'selection_notes',
      span: 6,
    },
    {
      type: 'title',
      label: '采购价',
    },
    {
      type: 'slot',
      label: '采购单价(含税)(供应商报价)',
      key: 'declared_purchase_tax_price',
      span: 6,
      slots: () => {
        // 提取值并转换为数字
        const price = Number(productInfo.value.declared_purchase_tax_price)
        // 当值为0时显示0.00，其他无有效值的情况显示--
        const displayValue = Number.isNaN(price) ? '--' : price.toFixed(2)
        return h('span', { style: { color: '#2b78f0' } }, ['￥', displayValue])
      },
    },
  ]

  /**
   * 采购价显示逻辑：
   * 1. 若商品选品状态为"待选品"(0)、"已驳回"(50)、"拒绝选品"(60)，采购价仅显示：含税单价(供应商报价)
   * 2. 若商品选品状态为"完成选品"(40)，采购价同时显示：含税单价(供应商报价)、含税单价(双方协商后定价)
   */
  if (shouldShowAgreedPrice.value) {
    baseFields.push({
      type: 'slot',
      label: '采购单价(含税)(双方协商后定价)',
      key: 'agreed_purchase_tax_price',
      span: 6,
      slots: () => h('span', { style: { color: '#d33333' } }, ['￥', productInfo.value.agreed_purchase_tax_price ? Number(productInfo.value.agreed_purchase_tax_price).toFixed(2) : '--']),
    })
  }

  // 添加申报供货价部分
  // baseFields.push(
  //   {
  //     type: 'title',
  //     label: '申报供货价',
  //   },
  //   {
  //     type: 'text',
  //     label: '不含税单价',
  //     key: 'unit_price',
  //     span: 6,
  //     afterSlot: () => h('span', null, ' CNY'),
  //   },
  //   {
  //     type: 'text',
  //     label: '税率',
  //     key: 'tax_rate',
  //     span: 6,
  //     afterSlot: () => h('span', null, ' %'),
  //   },
  //   {
  //     type: 'text',
  //     label: '含税单价',
  //     key: 'agreed_selling_price',
  //     span: 6,
  //     afterSlot: () => h('span', null, ' CNY'),
  //   },
  // )

  /**
   * 供货信息显示逻辑：
   * 若商品选品状态为"完成选品"(40)，显示供货信息：首批新品采购量、运费、发货时间、预计交期
   */
  if (shouldShowSupplyInfo.value) {
    baseFields.push(
      {
        type: 'title',
        label: '供货信息',
      },
      {
        type: 'slot',
        label: '首批新品采购量',
        key: 'first_batch_quantity',
        span: 6,
        slots: () => {
          const quantity = productInfo.value.first_batch_quantity
          if (!quantity) return h('span', null, '--')

          let packingQty: number | null = null

          if (!packingQty && productInfo.value.productAttrValues) {
            const packingQtyAttr = productInfo.value.productAttrValues.find((attr: any) => attr.attr_name === '标准装箱数')
            if (packingQtyAttr) {
              packingQty = Number(packingQtyAttr.value)
            }
          }

          if (packingQty && packingQty > 0) {
            const totalPcs = quantity * packingQty

            return h('span', null, `${quantity}箱（${totalPcs}pcs）`)
          }

          return h('span', null, `${quantity}箱（--）`)
        },
      },
      // {
      //   type: 'slot',
      //   label: '运费',
      //   key: 'shipping_fee',
      //   span: 6,
      //   slots: () => h('span', null, ['￥', productInfo.value.shipping_fee ? Number(productInfo.value.shipping_fee).toFixed(2) : '--']),
      // },
      {
        type: 'text',
        label: '发货时间',
        key: 'shipment_time',
        span: 6,
      },
      {
        type: 'text',
        label: '预计交期',
        key: 'expected_delivery_date',
        span: 6,
      },
    )
  }

  /**
   * 店铺供货价显示逻辑：
   * 1. 若商品选品状态为"待选品"(0)、"已驳回"(50)、"拒绝选品"(60)，不显示"店铺供货价"
   * 2. 若商品选品状态为"完成选品"(40)，显示"店铺供货价"
   */
  if (shouldShowStoreSupplyPrice.value) {
    baseFields.push(
      {
        type: 'title',
        label: '店铺供货价',
      },
      {
        type: 'slot',
        label: '店铺供货价',
        key: 'store_supply_price',
        span: 6,
        slots: () => h('span', null, ['￥', productInfo.value.store_supply_price ? Number(productInfo.value.store_supply_price).toFixed(2) : '--']),
      },
    )
  }

  // 添加模板配置
  baseFields.push(...templateFormConfig.value)

  return baseFields
})

const showFileList = ref<any[]>([])
// 商品所需许可证
const permitCloumns = ref([
  {
    field: 1,
    title: '化妆品生产许可证',
  },
  {
    field: 4,
    title: '食品生产许可证',
  },
  {
    field: 5,
    title: '药品生产许可证',
  },
  {
    field: 6,
    title: '医疗器械生产许可证',
  },
  {
    field: 8,
    title: '工业产品生产许可证',
  },
])

const userDrawerForm = ref<BaseFormItem[]>([
  {
    type: 'title',
    label: '其他信息',
  },
  {
    type: 'slot',
    label: '创建人',
    key: 'creator',
    span: 12,
    slots: () =>
      h('div', null, [
        h('span', null, productInfo.value.creator),
        (productInfo.value.job_of_creator || productInfo.value.depart_of_creator) &&
          h(
            'span',
            { class: 'c-#999 text-11 ml-4' },
            `( ${productInfo.value.job_of_creator || ''} ${productInfo.value.job_of_creator && productInfo.value.depart_of_creator ? '|' : ''} ${productInfo.value.depart_of_creator || ''} )`,
          ),
      ]),
  },
  {
    type: 'text',
    label: '创建时间',
    key: 'create_at',
    span: 12,
  },
  {
    type: 'slot',
    label: '修改人',
    key: 'modifier',
    span: 12,
    slots: () =>
      h('div', null, [
        h('span', null, productInfo.value.modifier),
        (productInfo.value.job_of_modifier || productInfo.value.depart_of_modifier) &&
          h(
            'span',
            { class: 'c-#999 text-11 ml-4' },
            `( ${productInfo.value.job_of_modifier || ''} ${productInfo.value.job_of_modifier && productInfo.value.depart_of_modifier ? '|' : ''} ${productInfo.value.depart_of_modifier || ''} )`,
          ),
      ]),
  },
  {
    type: 'text',
    label: '修改时间',
    key: 'modified_at',
    span: 12,
  },
])
// 商品主图
const fileObj = ref<any>({
  shopFileList: [],
  skuFileList: [],
  videoFileList: [],
  cnFileList: [],
  enFileList: [],
})

const language = ref('cn')
const fileSelectModalVisible = ref(false)

// 递归查找树形数据中的label
const findLabelByValue = (treeData: any[], targetValue: any, parentLabel = ''): string => {
  for (const item of treeData) {
    const currentLabel = parentLabel ? `${parentLabel} > ${item.name}` : item.name
    if (item.id === targetValue) return currentLabel
    if (item.children?.length) {
      const result = findLabelByValue(item.children, targetValue, currentLabel)
      if (result) return result
    }
  }
  return ''
}

// 获取商品类目选项
const getCategoryOption = async (rest: any) => {
  const res: any = await GetCategoryOption()
  // 查找并设置商品类目label
  if (rest.category_id && res.data) {
    const categoryLabel = findLabelByValue(res.data, rest.category_id)
    if (categoryLabel) rest.category_id = categoryLabel
  }
}

// 获取商品信息
const getInfo = async (id: number) => {
  const res = await GetProduct({ id, isEdit: props.isEdit })
  const { productAttrValues, ...rest } = res.data
  await getPermitFile(rest.productCertificates)
  productAttrValues.forEach((item: any) => {
    rest[item.attr_id] = item.value
  })
  rest.product_type = productTypeOptions.find((i) => i.value === rest.product_type)?.label

  // 处理商品类目显示
  await getCategoryOption(rest)

  await Promise.all([
    getFileObj(rest.main_images_ids, 'shopFileList', rest.main_images),
    getFileObj(rest.sku_images_ids, 'skuFileList', rest.sku_images),
    getFileObj(rest.video_file_id ? [rest.video_file_id] : [], 'videoFileList'),
    getFileObj(rest.product_detail_images_ids, 'cnFileList', rest.product_detail_images),
    getFileObj(rest.product_detail_en_images_ids, 'enFileList', rest.product_detail_en_images),
  ])

  rest.declared_purchase_tax_price = rest.declared_purchase_tax_price ? rest.declared_purchase_tax_price.toFixed(2) : '0'
  productInfo.value = rest
}

// 获取许可证
const getPermitFile = async (permitList: any[]) => {
  if (permitList.length) {
    await Promise.all(
      permitList.map(async (cert: any, index: number) => {
        const fileIdArr = cert.file_ids.split(',').filter(Boolean)
        const files = await Promise.all(
          fileIdArr.map(async (fid: string) => {
            const res = await ViewByFileIdCommon(fid)
            return {
              id: fid,
              status: 'done',
              url: URL.createObjectURL(res.data),
              file_id: fid,
            }
          }),
        )
        const fileRes = await GetFileList({ file_id_list: fileIdArr })
        files.forEach((file: any, idx: number) => {
          const name = fileRes.data[idx].original_name
          file.type = ['png', 'jpg', 'jpeg', 'gif', 'PNG', 'JPG', 'JPEG', 'GIF'].includes(name.split('.').pop()) ? 'image' : name.split('.').pop()
          file.name = name
        })
        permitList[index].fileList = files
      }),
    )
  }
}

const getFileObj = async (ids: number[], key: string, list?: any) => {
  if (!ids?.length) return
  if (list) {
    // 用 map 返回 Promise 数组，Promise.all 保证顺序
    const resArr = await Promise.all(
      list.map(async (element) => {
        const res = await ViewByFileIdCommon(element.file_id)
        return {
          id: element.file_id,
          fileId: element.file_id,
          status: 'done',
          url: URL.createObjectURL(res.data),
          name: element.file_name,
          type: ['png', 'jpg', 'jpeg', 'gif', 'PNG', 'JPG', 'JPEG', 'GIF'].includes(element.file_name.split('.').pop()) ? 'image' : element.file_name.split('.').pop(),
        }
      }),
    )
    fileObj.value[key].push(...resArr)
  } else {
    const fileDataList = await Promise.all(ids.map((id) => ViewByFileIdCommon(id)))
    const fileAttrRes = await GetFileList({ file_id_list: ids })
    // 用 Map 以 id 为 key 存储 fileAttrRes.data
    const attrMap = new Map(fileAttrRes.data.map((i) => [i.id, i]))
    fileObj.value[key] = await Promise.all(
      ids.map(async (id, idx) => {
        const i: any = attrMap.get(id)
        const url = URL.createObjectURL(fileDataList[idx].data)
        const cover = await getVideoCoverByStream(fileDataList[idx].data)
        return {
          id: i.id,
          fileId: i.id,
          status: 'done',
          url: cover,
          name: i.original_name,
          videoUrl: url,
          type: ['png', 'jpg', 'jpeg', 'gif', 'PNG', 'JPG', 'JPEG', 'GIF'].includes(i.original_name.split('.').pop()) ? 'image' : i.original_name.split('.').pop(),
        }
      }),
    )
  }
}
// 视频文件流获取封面
const getVideoCoverByStream = (file: Blob): Promise<string> => {
  return new Promise((resolve) => {
    const url = URL.createObjectURL(file)
    const video = document.createElement('video')
    video.src = url
    video.crossOrigin = 'anonymous'
    video.currentTime = 0.1 // 避免黑帧
    video.addEventListener(
      'canplay',
      () => {
        const canvas = document.createElement('canvas')
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        const ctx = canvas.getContext('2d')
        ctx?.drawImage(video, 0, 0, canvas.width, canvas.height)
        resolve(canvas.toDataURL('image/png'))
        URL.revokeObjectURL(url)
      },
      { once: true },
    )
  })
}

// 打开抽屉
const handleOpen = async (id: number) => {
  visible.value = true
  loading.value = true
  try {
    await getInfo(id)
    if (productInfo.value.category_template_id) {
      await getNewTemplate()
    }
  } finally {
    loading.value = false
  }
}

const getNewTemplate = async () => {
  const [transformTemplateConfig, templateConfig] = await getTemplate(productInfo.value.category_template_id)
  templateConfig.forEach(async (item: any) => {
    if (item.children?.length) {
      item.children.forEach((i: any) => {
        // 赋值选项的值，i.type_json.options[0].options为中文选项
        if (i.type_json.options?.length) {
          productInfo.value[i.id] = i.type_json.options[0].options.find((j: any) => j.value == productInfo.value[i.id])?.label
        }
        if (i.type_json.option_list?.length) {
          productInfo.value[i.id] = i.type_json.option_list.find((j: any) => j.value == productInfo.value[i.id])?.option_label_list[0].label
        }
        // 赋值单位，查找单位后value = value + unit
        if (i.template_display_unit) {
          const unit = findUnit(i.template_display_unit, i.type_json.unit_type).children
          if (unit && productInfo.value[i.id]) {
            productInfo.value[i.id] += ` ${unit}`
          }
        }
      })
    }
    if (item.type === 100) {
      const rows: any[] = []
      const ids: number[] = []
      item.children.forEach((i) => {
        const fileId = productInfo.value[i.id]
        if (!fileId) return
        ids.push(...fileId.split(',').map(Number))
        rows.push({
          id: i.id,
          fileId: fileId.split(',').map(Number),
        })
      })
      const fileAttrRes = await GetFileList({ file_id_list: ids.filter((i) => i) })
      rows.forEach(async (i) => {
        const res = await Promise.all(
          i.fileId.map(async (id) => {
            const res = await ViewByFileIdCommon(id)
            return { id, url: URL.createObjectURL(res.data), name: fileAttrRes.data.find((j) => j.id === id)?.original_name }
          }),
        )
        productInfo.value[i.id] = res.map((i) => ({
          id: i.id,
          url: i.url,
          name: i.name,
          status: 'done',
          type: ['png', 'jpg', 'jpeg', 'gif', 'PNG', 'JPG', 'JPEG', 'GIF'].includes(i.name.split('.').pop()) ? 'image' : i.name.split('.').pop(),
        }))
      })
    }
  })
  templateFormConfig.value = transformTemplateConfig
}

// 获取附件的数量
const getCount = (field: number) => {
  const list = productInfo.value?.productCertificates?.filter((i) => i.certificate_license_type === field)
  return list?.length || 0
}

const handleShowPermitModal = (field: number) => {
  const fileList: any[] = []
  const list = productInfo.value?.productCertificates?.filter((i) => i.certificate_license_type === field)
  list.forEach((i) => {
    fileList.push(...i.fileList)
  })
  showFileList.value = fileList || []
  fileSelectModalVisible.value = true
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
  productInfo.value = {}
  templateFormConfig.value = []
  // basicDrawerForm 现在是计算属性，不需要手动清理
}

// 自定义插槽回调
const slotCallback = (item: any): BaseFormItem => ({
  type: 'slot',
  label: '',
  key: item.name,
  slots: () => h(ProductAddStepSecondTable, { item, form: productInfo.value, disabled: true }),
})

// const handleDownload = async (type: string) => {
//   const res = await DownloadImagesZip(productInfo.value.id, type === 'shopFileList' ? 1 : 2)
//   const blob = new Blob([res.data], { type: 'application/zip' })
//   const url = URL.createObjectURL(blob)
//   window.open(url)
// }
const handleDownload = async (type: string) => {
  try {
    const keyMap = {
      shopFileList: 1,
      skuFileList: 2,
      detailFileList: 3,
    }
    const fileNameMap = {
      shopFileList: '商品主图压缩包',
      skuFileList: '商品SKU图压缩包',
      detailFileList: '商品详情图片压缩包',
    }
    const res = await DownloadImagesZip({ id: productInfo.value.id, type: keyMap[type] })
    const blob = new Blob([res.data], { type: 'application/zip' })
    const url = URL.createObjectURL(blob)

    // 创建a标签触发下载（替代window.open，解决文件名问题）
    const a = document.createElement('a')
    a.href = url
    const orderNumber = productInfo.value.supplier_product_number || productInfo.value.product_number || ''
    a.download = `${fileNameMap[type]}${orderNumber && `【${orderNumber}】`}.zip` // 设置默认文件名
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载失败:', error)
    message.error('图片下载失败')
  }
}

const [BaseForm] = useBaseForm({
  modelValue: productInfo,
  formConfig: basicDrawerForm,
  isText: true,
})

const [UserForm] = useBaseForm({
  modelValue: productInfo,
  formConfig: userDrawerForm,
  isText: true,
})

const { getTemplate, findUnit } = useTemplate(slotCallback)

onMounted(() => {
  handleOpen(props.id)
})
</script>

<style scoped lang="scss">
:deep(.base-file-upload) {
  .ant-upload.ant-upload-select {
    width: 58px !important;
    height: 58px !important;
  }

  .ant-upload-list-item-container {
    width: 58px !important;
    height: 58px !important;
    overflow: hidden !important;
  }

  .ant-upload-list-item {
    padding: 0 !important;

    &::before {
      width: 58px !important;
      height: 58px !important;
    }
  }
}

:deep(.main-image) {
  .ant-upload-list {
    .ant-upload-list-item-container {
      &:first-child {
        position: relative;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          width: 100%;
          height: 20px;
          font-size: 10px;
          line-height: 18px;
          color: #fff;
          text-align: center;
          content: '首张主图';
          background-color: #73a2f3;
        }
      }
    }
  }
}

:deep(.vxe-table--body-wrapper) {
  min-height: 0 !important;
}

.textColor1 {
  color: #999;
}

.textColor2 {
  color: #999;
}

.textColor3 {
  color: #999;
}

.textColor4 {
  color: #999;
}

.textColor5 {
  color: #d33333;
}
</style>
