<template>
  <div class="div-grid" :class="{ border: props.border }" :style="{ minHeight: props.minHeight + 'px' }" @click.stop>
    <!-- header -->
    <div
      v-if="showHeader"
      class="div-grid-header"
      :style="{
        'grid-template-columns': `repeat(${girdColumns.length}, 0fr)`,
        'padding-left': leftConfig.pull + 'px',
      }"
    >
      <div
        class="div-grid-header-item absolute!"
        v-for="(column, columnIndex) in leftConfig.list"
        :key="column.field"
        :style="{ width: column.width + 'px', textAlign: column.align || 'left', ...setStyle(columnIndex, 'left') }"
      >
        <span>{{ column.title }}</span>
      </div>
      <div
        class="div-grid-header-item"
        v-for="column in girdColumns.filter((f) => !f.fixed)"
        :key="column.field"
        :style="{
          width: column.width + 'px',
          textAlign: column.align || 'left',
        }"
        @mouseleave="mouseleaveByItem"
      >
        <span :title="column.title">{{ column.title }}</span>
        <ExclamationCircleOutlined v-if="column.tooltip" class="ml-5 cursor-pointer" @mouseenter="(e) => mouseenterByItem(e, column)" />
        <!-- 列宽调整拖拽手柄 -->
        <div class="column-resize-handle" @mousedown="(e) => startResize(e, column)" @dblclick="() => autoResize(column)"></div>
      </div>
      <div class="div-grid-header-item" v-for="column in girdColumns.filter((f) => f.fixed === 'right')" :key="column.field" :style="{ width: column.width + 'px', textAlign: column.align || 'left' }">
        {{ column.title }}
      </div>
    </div>
    <!-- body -->
    <div v-if="gridData?.length" class="div-grid-body bg-#fff" :style="{ 'padding-left': leftConfig.pull + 'px', maxHeight: Math.min(props.height, innerHeight) + 'px' }">
      <div
        class="div-grid-body-row"
        v-for="(row, index) in gridData"
        :key="row[props.keyField || '']"
        :style="{
          'grid-template-columns': `repeat(${girdColumns.length}, 0fr)`,
        }"
      >
        <div
          class="div-grid-body-item absolute!"
          v-for="(column, columnIndex) in leftConfig.list"
          :key="column.field"
          :style="{
            width: column.width + 'px',
            height: `${props.rowHeight}px`,
            ...setStyle(columnIndex, 'left'),
            textAlign: column.align || 'left',
            justifyContent: getJustifyContent(column.align),
          }"
        >
          <span v-if="column.type === 'seq'">{{ `${props.index ? `${props.index}-` : ''}${index + 1}` }}</span>
          <template v-else-if="column.cellRender || column.render">
            <component :is="cellRender(column, row, index)"></component>
          </template>
          <span v-else :style="column.style || {}" :title="renderItemCell({ row, column })">
            {{ renderItemCell({ row, column }) }}
          </span>
        </div>
        <div
          class="div-grid-body-item"
          v-for="column in girdColumns.filter((f) => !f.fixed || f.fixed === 'right')"
          :key="column.field"
          :style="{
            width: column.width + 'px',
            height: `${props.rowHeight}px`,
            textAlign: column.align || 'left',
            justifyContent: getJustifyContent(column.align),
          }"
        >
          <template v-if="column.cellRender || column.render">
            <component :is="cellRender(column, row, index)"></component>
          </template>
          <span :style="column.style || {}" :title="renderItemCell({ row, column })" v-else>
            {{ renderItemCell({ row, column }) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import { formatMap } from '@/utils/formatter'
import { imageRender, imageByIdRender, textRender, copyRender } from '@/utils/VxeRender'

const props = withDefaults(
  defineProps<{
    columns?: Array<any>
    data?: Array<any>
    keyField?: string
    rowHeight?: number
    index?: number
    height?: number
    minHeight?: number
    parentRow?: any
    border?: boolean
    showHeader?: boolean
  }>(),
  {
    columns: () => [],
    data: () => [],
    rowHeight: 32,
    height: 100,
    minHeight: 50,
    border: false,
    showHeader: true,
  },
)

const innerHeight = ref(window.innerHeight - 100)

// 根据对齐方式获取justify-content值
const getJustifyContent = (align: string) => {
  switch (align) {
    case 'center':
      return 'center'
    case 'right':
      return 'flex-end'
    case 'left':
    default:
      return 'flex-start'
  }
}

const cellFormatter = (row, column) => {
  if (!column.formatter) return undefined
  const formatter = column.formatter && (typeof column.formatter === 'string' ? formatMap[column.formatter] : column.formatter)
  return formatter({ cellValue: row[column.field], value: row[column.field], row })
}

const girdColumns = ref<Record<string, any>[]>([])
watch(
  () => props.columns,
  () => {
    girdColumns.value = props.columns
  },
  { immediate: true },
)
const gridData = ref<Record<string, any>[]>(props.data)
watch(
  () => props.data,
  () => {
    gridData.value = props.data
  },
  { immediate: true },
)
const leftConfig = computed(() => {
  const list = girdColumns.value.filter((i) => i.fixed == 'left')
  return {
    list,
    length: list.length,
    pull: list.reduce((acc, cur) => {
      return acc + cur.width
    }, 0),
  }
})

const cellRender = (column, row, index) => {
  const target = {
    image: imageRender,
    imageById: imageByIdRender,
    text: textRender,
    copy: copyRender,
  }[column?.cellRender?.name]
  return {
    render: () => {
      return target
        ? target(undefined, {
            row,
            column,
            parentRow: props.parentRow,
          })
        : column.render
          ? column.render({ row, column, parentRow: props.parentRow, rowIndex: index })
          : null
    },
  }
}

const renderItemCell = ({ column, row }) => {
  return cellFormatter(row, column) ?? row[column.field]
}

const setStyle = (index, direction) => {
  return {
    [direction]: `${leftConfig.value.list.slice(0, index).reduce((acc, cur) => {
      return acc + cur.width
    }, 0)}px`,
    zIndex: 100,
  }
}

const updateGrid = ({ columns, data }) => {
  if (columns) {
    girdColumns.value = columns
  }
  if (data) {
    gridData.value = data
  }
}

// 列宽调整相关状态
const isResizing = ref(false)
const resizingColumn = ref(null)
const startX = ref(0)
const startWidth = ref(0)

// 开始调整列宽
const startResize = (e, column) => {
  e.preventDefault()
  e.stopPropagation()

  isResizing.value = true
  resizingColumn.value = column
  startX.value = e.clientX
  startWidth.value = column.width

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.body.style.cursor = 'col-resize'
}

// 处理列宽调整
const handleResize = (e) => {
  if (!isResizing.value || !resizingColumn.value) return

  const deltaX = e.clientX - startX.value
  const newWidth = Math.max(50, startWidth.value + deltaX) // 最小宽度50px

  resizingColumn.value.width = newWidth
}

// 停止调整列宽
const stopResize = () => {
  isResizing.value = false
  resizingColumn.value = null

  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.cursor = ''
}

// 自动调整列宽（双击）
const autoResize = (column) => {
  // 简单的自动调整：根据标题长度设置宽度
  const titleLength = column.title.length
  const minWidth = Math.max(80, titleLength * 12 + 40)
  column.width = minWidth
}

onBeforeUnmount(() => {
  // 清理事件监听器
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
})

defineExpose({ updateGrid })
</script>

<style lang="scss" scoped>
.div-grid {
  color: var(--vxe-ui-font-color);
  box-shadow: 0 0 3px rgb(0 0 0 / 20%) inset;

  &.border {
    .div-grid-header-item {
      border-top: 1px #eaecee solid;

      &:first-child {
        border-left: 1px #eaecee solid;
      }
    }

    .div-grid-body-item {
      display: flex;
      align-items: center;

      &:first-child {
        border-left: 1px #eaecee solid;
      }
    }
  }

  &-header {
    @apply grid relative;

    &-item {
      z-index: 1;
      padding-right: var(--vxe-ui-table-cell-padding-right);
      padding-left: var(--vxe-ui-table-cell-padding-left);
      font-weight: bold;
      color: rgb(0 0 0 / 75%);
      background-color: #f8f8f9;
      border-right: 1px #eaecee solid;
      border-bottom: 1px #eaecee solid;

      @apply relative h-30 pr-5 overflow-hidden whitespace-nowrap text-ellipsis lh-30;

      // 列宽调整手柄
      .column-resize-handle {
        position: absolute;
        top: 0;
        right: -2px;
        z-index: 10;
        width: 4px;
        height: 100%;
        cursor: col-resize;
        background: transparent;

        &:hover {
          background: #409eff;
        }

        &:active {
          background: #337ecc;
        }
      }

      &:hover .column-resize-handle {
        background: rgb(64 158 255 / 30%);
      }
    }
  }

  &-body {
    &-row {
      @apply grid;
    }

    &-item {
      z-index: 1;
      padding: 5px var(--vxe-ui-table-cell-padding-right) 5px var(--vxe-ui-table-cell-padding-left);
      border-right: 1px #eaecee solid;
      border-bottom: 1px #eaecee solid;

      @apply relative overflow-hidden whitespace-nowrap text-ellipsis lh-18;
    }
  }
}
</style>
